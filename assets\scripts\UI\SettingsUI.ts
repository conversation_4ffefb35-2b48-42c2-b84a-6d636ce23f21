import { _decorator, Component, Node, Label, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, director, AudioClip, assetManager, UIOpacity } from 'cc';
import { AudioMgr } from '../AudioMgr';
import { InviteCodeManager } from '../InviteCodeManager';
import { WeChatClipboard } from '../Utils/WeChatClipboard';
const { ccclass, property } = _decorator;

// 微信小游戏API类型声明
declare global {
    interface Window {
        wx?: {
            setClipboardData: (options: {
                data: string;
                success?: () => void;
                fail?: (err: any) => void;
            }) => void;
            showToast: (options: {
                title: string;
                icon?: 'success' | 'loading' | 'none';
                duration?: number;
                success?: () => void;
                fail?: (err: any) => void;
            }) => void;
        };
    }
    const wx: {
        setClipboardData: (options: {
            data: string;
            success?: () => void;
            fail?: (err: any) => void;
        }) => void;
        showToast: (options: {
            title: string;
            icon?: 'success' | 'loading' | 'none';
            duration?: number;
            success?: () => void;
            fail?: (err: any) => void;
        }) => void;
    } | undefined;
}

@ccclass('SettingsUI')
export class SettingsUI extends Component {
    
    // UI节点引用
    @property(Label)
    myInviteCodeLabel: Label = null;
    
    @property(Node)
    inviteInputPanel: Node = null;
    
    @property(EditBox)
    inviteCodeInput: EditBox = null;
    
    @property(Button)
    confirmButton: Button = null;
    
    @property(Button)
    copyButton: Button = null;
    
    @property(Node)
    successTipSprite: Node = null;

    @property(Node)
    errorTipSprite: Node = null;
    
    // 背景音乐
    @property(AudioClip)
    bgAudio: AudioClip = null;
    
    onLoad() {
        console.log("SettingsUI onLoad");
        
        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();
        
        // 播放背景音乐
        if (this.bgAudio) {
            this.playSettingsBGM();
        } else {
            this.tryGetBgAudio();
        }
    }
    
    start() {
        this.initializeUI();
        this.setupButtonEvents();
        this.setupInputEvents();
        this.startTimeCheck();
    }
    
    /**
     * 初始化UI显示
     */
    private initializeUI(): void {
        // 显示玩家的邀请码（只显示6位邀请码）
        const playerInviteCode = InviteCodeManager.getPlayerInviteCode();
        if (this.myInviteCodeLabel) {
            this.myInviteCodeLabel.string = playerInviteCode;
        }
        
        // 检查是否应该显示邀请码输入界面
        const shouldShowInput = InviteCodeManager.shouldShowInviteInput();
        if (this.inviteInputPanel) {
            this.inviteInputPanel.active = shouldShowInput;
        }
        
        // 初始化提示精灵状态
        this.initializeTipSprites();

        // 更新确认按钮状态
        this.updateConfirmButtonState();
        
        console.log("设置界面初始化完成");
        console.log("邀请码系统状态:", InviteCodeManager.getInviteSystemStatus());
    }
    
    /**
     * 初始化提示精灵状态
     */
    private initializeTipSprites(): void {
        if (this.successTipSprite) {
            this.successTipSprite.active = false;
        }
        if (this.errorTipSprite) {
            this.errorTipSprite.active = false;
        }
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvents(): void {
        // 确认按钮事件
        if (this.confirmButton) {
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClick, this);
        }

        // 复制按钮事件
        if (this.copyButton) {
            this.copyButton.node.on(Button.EventType.CLICK, this.onCopyButtonClick, this);
        }
    }

    /**
     * 设置输入框事件
     */
    private setupInputEvents(): void {
        if (this.inviteCodeInput) {
            // 监听输入框内容变化
            this.inviteCodeInput.node.on('text-changed', this.onInputTextChanged, this);
        }
    }
    
    /**
     * 输入框内容变化事件
     */
    private onInputTextChanged(): void {
        this.updateConfirmButtonState();
    }

    /**
     * 更新确认按钮状态
     */
    private updateConfirmButtonState(): void {
        if (!this.confirmButton || !this.inviteCodeInput) return;

        const inputText = this.inviteCodeInput.string.trim();
        const isEmpty = inputText.length === 0;

        // 设置按钮交互状态
        this.confirmButton.interactable = !isEmpty;

        // 可选：设置按钮透明度来视觉上表示状态
        const buttonNode = this.confirmButton.node;
        if (buttonNode) {
            // 使用UIOpacity组件来设置透明度
            let uiOpacity = buttonNode.getComponent(UIOpacity);
            if (!uiOpacity) {
                uiOpacity = buttonNode.addComponent(UIOpacity);
            }
            uiOpacity.opacity = isEmpty ? 128 : 255; // 空时半透明，有内容时完全不透明
        }
    }

    /**
     * 确认按钮点击事件
     */
    private onConfirmButtonClick(): void {
        if (!this.inviteCodeInput) {
            console.error("邀请码输入框未设置");
            return;
        }

        const inputCode = this.inviteCodeInput.string.trim(); // 保持原始大小写

        if (inputCode.length === 0) {
            // 输入为空时不应该能点击，但作为保险
            return;
        }

        // 尝试使用邀请码
        const success = InviteCodeManager.useInviteCode(inputCode);

        if (success) {
            this.showSuccessTip();
            // 隐藏输入面板
            if (this.inviteInputPanel) {
                this.inviteInputPanel.active = false;
            }
            // 清空输入框
            this.inviteCodeInput.string = "";
        } else {
            this.showErrorTip();
        }
    }
    
    /**
     * 复制按钮点击事件
     */
    private onCopyButtonClick(): void {
        const playerInviteCode = InviteCodeManager.getPlayerInviteCode();

        // 使用专门的微信小游戏剪贴板工具
        WeChatClipboard.copyText(
            playerInviteCode,
            () => {
                // 成功回调
                console.log("复制成功回调");
                this.showCopySuccess();
            },
            (error: string) => {
                // 失败回调
                console.log("复制失败回调:", error);
                // WeChatClipboard已经处理了失败提示，这里不需要额外处理
            }
        );
    }

    // 旧的复制方法已被WeChatClipboard工具类替代

    // 旧的fallback复制方法已被WeChatClipboard工具类替代

    /**
     * 显示复制成功提示
     */
    private showCopySuccess(): void {
        console.log("复制成功！邀请码已复制到剪贴板");
        // WeChatClipboard工具类已经处理了成功提示
        // 这里可以添加额外的UI反馈，比如显示成功图标等
    }
    
    /**
     * 显示成功提示
     */
    private showSuccessTip(): void {
        if (this.successTipSprite) {
            this.successTipSprite.active = true;

            // 2秒后隐藏
            this.scheduleOnce(() => {
                if (this.successTipSprite) {
                    this.successTipSprite.active = false;
                }
            }, 2);
        }
        console.log(`成功使用邀请码！获得${InviteCodeManager.getInviteReward()}金币奖励`);
    }

    /**
     * 显示错误提示
     */
    private showErrorTip(): void {
        if (this.errorTipSprite) {
            this.errorTipSprite.active = true;

            // 2秒后隐藏
            this.scheduleOnce(() => {
                if (this.errorTipSprite) {
                    this.errorTipSprite.active = false;
                }
            }, 2);
        }
        console.log("邀请码无效或已使用过");
    }
    
    /**
     * 返回主菜单按钮点击事件
     */
    public onHomeButtonClick(): void {
        console.log("SettingsUI: 返回主菜单");
        
        // 不再停止音乐，让主菜单场景自己处理BGM的播放
        // 加载Home场景
        director.loadScene('Home');
    }
    
    /**
     * 播放设置界面BGM
     */
    private playSettingsBGM(): void {
        console.log("尝试播放设置界面BGM");
        
        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();
        
        if (this.bgAudio) {
            // 延迟一帧再播放，确保音频系统已准备好
            this.scheduleOnce(() => {
                console.log("播放设置界面BGM");
                AudioMgr.inst.play(this.bgAudio, 0.1);
            }, 0);
        }
    }
    
    /**
     * 尝试在运行时获取bgAudio
     */
    private tryGetBgAudio(): void {
        // 使用主菜单的BGM作为设置界面的BGM
        const HOME_BGM_UUID = "2d384416-f267-40be-9fb8-f091110d84c6";
        
        try {
            console.log("尝试通过UUID加载设置界面BGM:", HOME_BGM_UUID);
            assetManager.loadAny({uuid: HOME_BGM_UUID}, (err, asset) => {
                if (err) {
                    console.error("通过UUID加载设置界面BGM失败:", err);
                    return;
                }
                
                console.log("通过UUID加载设置界面BGM成功");
                this.bgAudio = asset as AudioClip;
                
                // 加载成功后立即播放
                this.playSettingsBGM();
            });
        } catch (error) {
            console.error("获取设置界面bgAudio失败:", error);
        }
    }
    
    /**
     * 开始定时检查（测试用）
     */
    private startTimeCheck(): void {
        // 每5秒检查一次时间状态
        this.schedule(() => {
            this.checkAndUpdateInputPanelVisibility();
        }, 5);

        console.log("开始定时检查输入面板状态（每5秒检查一次）");
    }

    /**
     * 检查并更新输入面板可见性
     */
    private checkAndUpdateInputPanelVisibility(): void {
        if (!this.inviteInputPanel) return;

        const shouldShowInput = InviteCodeManager.shouldShowInviteInput();
        const currentlyVisible = this.inviteInputPanel.active;

        if (currentlyVisible && !shouldShowInput) {
            // 应该隐藏但当前显示
            this.inviteInputPanel.active = false;
            console.log("时间到期，自动隐藏邀请码输入面板");
        } else if (!currentlyVisible && shouldShowInput) {
            // 应该显示但当前隐藏
            this.inviteInputPanel.active = true;
            console.log("条件满足，自动显示邀请码输入面板");
        }

        // 输出当前状态用于调试
        const status = InviteCodeManager.getInviteSystemStatus();
        console.log(`定时检查: 是否首次注册=${status.isFirstDay}, 是否已使用=${status.hasUsedCode}, 应该显示=${shouldShowInput}, 当前显示=${currentlyVisible}`);
    }

    protected onDestroy(): void {
        // 清理定时器
        this.unscheduleAllCallbacks();

        // 清理事件监听
        if (this.confirmButton && this.confirmButton.node && this.confirmButton.node.isValid) {
            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmButtonClick, this);
        }

        if (this.copyButton && this.copyButton.node && this.copyButton.node.isValid) {
            this.copyButton.node.off(Button.EventType.CLICK, this.onCopyButtonClick, this);
        }

        if (this.inviteCodeInput && this.inviteCodeInput.node && this.inviteCodeInput.node.isValid) {
            this.inviteCodeInput.node.off('text-changed', this.onInputTextChanged, this);
        }
    }
}
