import { _decorator, Component, Input, input, Node, director } from 'cc';
import { GameManager } from '../GameManager';
import { AudioMgr } from '../AudioMgr';
const { ccclass, property } = _decorator;

@ccclass('GameReadyUI')
export class GameReadyUI extends Component {
    protected onLoad(): void {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
    }
    onDestroy () {
        // 移除输入事件监听器
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);

        // 取消所有调度器
        this.unscheduleAllCallbacks();

        console.log("GameReadyUI: 组件已销毁，事件监听器已清理");
    }

    private _touchProcessed: boolean = false;

    onTouchStart(){
        // 检查节点是否激活，如果没有激活则不处理触摸事件
        if (!this.node.active) {
            return;
        }

        // 防止重复处理触摸事件
        if (this._touchProcessed) {
            return;
        }

        this._touchProcessed = true;
        console.log("GameReadyUI: 处理开始游戏触摸事件");

        // 延迟一帧再调用，避免在同一帧内多次触发
        this.scheduleOnce(() => {
            GameManager.inst().transitionToGamingState();

            // 重置标志，允许下一次触摸，缩短时间为0.2秒
            this.scheduleOnce(() => {
                this._touchProcessed = false;
                console.log("GameReadyUI: 重置触摸处理标志");
            }, 0.2); // 从1.0秒缩短到0.2秒
        }, 0);
    }

    /**
     * 点击返回主页按钮的处理方法
     * 在编辑器中将此方法绑定到HomeButton的点击事件
     */
    onHomeButtonClick() {
        console.log("GameReadyUI: 返回主菜单");

        // 不再停止音乐，让主菜单场景自己处理BGM的播放
        // 注意：我们不再调用AudioMgr.inst.stop()

        // 加载Home场景
        director.loadScene('Home');
    }
}


