import { _decorator, Component, Node } from 'cc';
import { GameDataManager } from './Data/GameDataManager';
const { ccclass, property } = _decorator;

/**
 * 游戏初始化器
 * 在游戏启动时自动初始化所有必要的系统
 */
@ccclass('GameInitializer')
export class GameInitializer extends Component {

    onLoad() {
        console.log('GameInitializer: 开始初始化游戏系统...');
        this.initializeGameSystems();
    }

    /**
     * 初始化游戏系统
     */
    private async initializeGameSystems(): Promise<void> {
        try {
            // 1. 首先初始化微信云开发
            await this.initializeWeChatCloud();
            
            // 2. 初始化游戏数据管理器（包含登录功能）
            const gameDataManager = GameDataManager.instance;
            const success = await gameDataManager.initialize();

            if (success) {
                // 3. 设置自动数据同步
                gameDataManager.setupAutoSync();
                console.log('GameInitializer: 游戏系统初始化成功');
                
                // 3. 同步本地数据到云端（可选）
                await this.syncDataIfNeeded();
            } else {
                console.error('GameInitializer: 游戏系统初始化失败');
            }
            
        } catch (error) {
            console.error('GameInitializer: 初始化过程中出错', error);
        }
    }

    /**
     * 初始化微信云开发
     */
    private async initializeWeChatCloud(): Promise<void> {
        try {
            if (typeof wx !== 'undefined' && wx.cloud) {
                await wx.cloud.init({
                    env: 'cloud1-9gx9hhw6416d5994'
                });
                console.log('GameInitializer: 微信云开发初始化成功');
            } else {
                console.warn('GameInitializer: 微信云开发不可用（可能在非微信环境中运行）');
            }
        } catch (error) {
            console.error('GameInitializer: 微信云开发初始化失败', error);
        }
    }

    /**
     * 根据需要同步数据
     */
    private async syncDataIfNeeded(): Promise<void> {
        try {
            const gameDataManager = GameDataManager.instance;
            
            // 检查是否需要从云端加载数据
            const cloudData = await gameDataManager.getCloudPlayerData();
            if (cloudData) {
                console.log('GameInitializer: 发现云端数据，开始同步到本地');
                await gameDataManager.loadCloudDataToLocal();
            } else {
                console.log('GameInitializer: 云端无数据，将本地数据同步到云端');
                await gameDataManager.syncLocalDataToCloud();
            }
            
        } catch (error) {
            console.error('GameInitializer: 数据同步失败', error);
        }
    }
}
