import { _decorator } from 'cc';
import { CloudDatabaseManager } from './CloudDatabaseManager';
import { DataSyncManager } from './DataSyncManager';
import { GameMode, GameData } from '../GameData';
import { WeChatLoginManager } from '../WeChatLoginManager';
import { InviteCodeManager } from '../InviteCodeManager';
const { ccclass } = _decorator;

/**
 * 游戏数据管理器
 * 统一处理本地游戏数据与云数据库的同步
 */
@ccclass('GameDataManager')
export class GameDataManager {
    private static _instance: GameDataManager = null;
    private cloudDB: CloudDatabaseManager = null;
    private syncManager: DataSyncManager = null;
    private isInitialized: boolean = false;

    public static get instance(): GameDataManager {
        if (!this._instance) {
            this._instance = new GameDataManager();
        }
        return this._instance;
    }

    /**
     * 初始化游戏数据管理器
     */
    public async initialize(): Promise<boolean> {
        if (this.isInitialized) {
            return true;
        }

        try {
            // 1. 初始化微信登录管理器
            const loginManager = WeChatLoginManager.instance;
            if (!loginManager) {
                console.error("GameDataManager: 无法获取WeChatLoginManager实例");
                return false;
            }

            const loginInitSuccess = await loginManager.initialize();

            if (!loginInitSuccess) {
                console.error("GameDataManager: 微信登录初始化失败");
                return false;
            }

            // 2. 初始化云数据库和同步管理器
            this.cloudDB = CloudDatabaseManager.instance;
            this.syncManager = DataSyncManager.instance;

            // 初始化云数据库
            const cloudInitSuccess = await this.cloudDB.initialize();
            if (!cloudInitSuccess) {
                console.error('GameDataManager: 云数据库初始化失败');
                return false;
            }

            // 3. 检查登录状态
            if (loginManager.isLoggedIn()) {
                console.log("GameDataManager: 用户登录成功，数据同步已完成");
            } else {
                console.error("GameDataManager: 用户登录失败，无法继续初始化");
                return false;
            }

            this.isInitialized = true;

            // 启动游戏生命周期同步监听
            this.startGameLifecycleSync();

            console.log('GameDataManager: 初始化成功');
            return true;
        } catch (error) {
            console.error('GameDataManager: 初始化失败', error);
            return false;
        }
    }



    /**
     * 实时同步玩家数据到云端（本地API调用）
     * 当玩家数据发生变化时调用此方法
     */
    public async syncPlayerDataToCloud(): Promise<void> {
        try {
            const loginManager = WeChatLoginManager.instance;

            // 只有登录用户才同步数据
            if (!loginManager.isLoggedIn()) {
                console.log("GameDataManager: 用户未登录，跳过数据同步");
                return;
            }

            console.log("GameDataManager: 开始同步玩家数据到云端");

            // 准备要同步的数据
            const userData = {
                nickname: loginManager.getUserInfo()?.nickname || '新玩家',
                avatarUrl: loginManager.getUserInfo()?.avatarUrl || '1_penguin_home',
                coins: GameData.getCoin(),
                inviteCode: InviteCodeManager.getPlayerInviteCode(),
                topScores: {
                    [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                    [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                    [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                    [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                    [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                    [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
                },
                updateTime: new Date()
            };

            // 使用本地API上传数据（不使用云函数）
            const success = await this.cloudDB.createOrUpdateUserData(userData);

            if (success) {
                console.log("GameDataManager: 玩家数据同步成功");
            } else {
                console.error("GameDataManager: 玩家数据同步失败");
            }

        } catch (error) {
            console.error("GameDataManager: 同步玩家数据到云端失败", error);
        }
    }

    // 自动同步开关
    private autoSyncEnabled: boolean = true;

    /**
     * 监听游戏数据变化并自动同步
     * 注意：只有setCoin和setBestScore会触发自动同步，addCoin不会（避免游戏中频繁同步）
     */
    public setupAutoSync(): void {
        // 监听金币设置变化（用于云端数据同步到本地）
        const originalSetCoin = GameData.setCoin;
        GameData.setCoin = (coins: number) => {
            originalSetCoin.call(GameData, coins);
            if (this.autoSyncEnabled) {
                console.log("GameDataManager: setCoin触发自动同步");
                this.syncPlayerDataToCloud(); // 自动同步
            }
        };

        // 注意：addCoin不设置自动同步，避免游戏中频繁调用
        // addCoin只在游戏进行中收集金币时调用，应该在游戏结束时统一同步

        // 监听分数变化（用于云端数据同步到本地）
        const originalSetBestScore = GameData.setBestScore;
        GameData.setBestScore = (mode: GameMode, score: number) => {
            originalSetBestScore.call(GameData, mode, score);
            if (this.autoSyncEnabled) {
                console.log("GameDataManager: setBestScore触发自动同步");
                this.syncPlayerDataToCloud(); // 自动同步
            }
        };

        console.log("GameDataManager: 自动同步监听已设置（不包括addCoin）");
    }

    /**
     * 启用/禁用自动同步
     */
    public setAutoSyncEnabled(enabled: boolean): void {
        this.autoSyncEnabled = enabled;
        console.log(`GameDataManager: 自动同步${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 更新游戏分数
     */
    public async updateScore(gameMode: GameMode, score: number): Promise<boolean> {
        if (!this.isInitialized) {
            console.error('GameDataManager: 未初始化，无法更新分数');
            return false;
        }

        try {
            // 先更新本地GameData
            const currentBest = GameData.getBestScore(gameMode);
            if (score > currentBest) {
                GameData.setBestScore(gameMode, score);
                console.log(`GameDataManager: 本地新纪录！${gameMode}: ${score}`);
            }

            // 延迟同步到云数据库
            this.syncManager.requestScoreSync(gameMode, score);
            return true;
        } catch (error) {
            console.error('GameDataManager: 更新分数失败', error);
            return false;
        }
    }

    /**
     * 立即同步分数到云数据库（游戏结束时调用）
     */
    public async syncScoreImmediately(gameMode: GameMode, score: number): Promise<boolean> {
        if (!this.isInitialized) {
            return false;
        }

        return await this.syncManager.syncScoreImmediately(gameMode, score);
    }

    /**
     * 更新金币
     */
    public updateCoins(amount: number, operation: 'add' | 'set' = 'add'): void {
        if (!this.isInitialized) {
            console.error('GameDataManager: 未初始化，无法更新金币');
            return;
        }

        try {
            // 先更新本地GameData
            if (operation === 'add') {
                GameData.addCoin(amount);
            } else {
                GameData.setCoin(amount);
            }

            // 延迟同步到云数据库
            this.syncManager.requestCoinsSync(amount, operation);
        } catch (error) {
            console.error('GameDataManager: 更新金币失败', error);
        }
    }

    /**
     * 立即同步金币到云数据库
     */
    public async syncCoinsImmediately(amount: number, operation: 'add' | 'set' = 'add'): Promise<boolean> {
        if (!this.isInitialized) {
            return false;
        }

        return await this.syncManager.syncCoinsImmediately(amount, operation);
    }

    /**
     * 游戏结束时同步数据（推荐在游戏结束时调用）
     * � 终极优化：一次云调用同步所有数据
     */
    public async syncGameEndData(gameMode: GameMode, finalScore: number): Promise<boolean> {
        if (!this.isInitialized) {
            console.error('GameDataManager: 未初始化，无法同步游戏结束数据');
            return false;
        }

        try {
            console.log(`GameDataManager: 开始同步游戏结束数据 - 模式:${gameMode}, 分数:${finalScore}`);

            // � 终极优化：收集所有本地数据，一次性同步到云端
            const allTopScores = {
                [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
            };

            const totalCoins = GameData.getCoin();

            console.log(`GameDataManager: 一次性同步所有数据 - 当前关卡:${gameMode}, 分数:${finalScore}, 金币:${totalCoins}`);
            console.log(`GameDataManager: 同步所有关卡分数:`, allTopScores);

            // 🚀 一次云调用解决所有同步
            const success = await this.cloudDB.updateAllPlayerData(allTopScores, totalCoins);

            if (success) {
                console.log('GameDataManager: 游戏结束数据同步完成（一次调用）');
                return true;
            } else {
                console.error('GameDataManager: 游戏结束数据同步失败');
                return false;
            }
        } catch (error) {
            console.error('GameDataManager: 同步游戏结束数据失败', error);
            return false;
        }
    }

    /**
     * 🎯 智能同步游戏结束数据（按需优化版本）
     */
    public async syncGameEndDataSmart(gameMode: GameMode, finalScore: number): Promise<boolean> {
        if (!this.isInitialized) {
            console.error('GameDataManager: 未初始化，无法同步游戏结束数据');
            return false;
        }

        // 🔧 重要：暂时禁用自动同步，防止云端数据覆盖刚更新的本地数据
        const originalAutoSync = this.autoSyncEnabled;
        this.setAutoSyncEnabled(false);

        try {
            console.log(`GameDataManager: 开始智能同步游戏结束数据 - 模式:${gameMode}, 分数:${finalScore}`);

            // 🎯 步骤1：简化逻辑 - 总是同步游戏记录
            // 🔧 最简修复：由于saveScore()总是会更新topScores，为确保数据一致性，总是同步
            // 这样避免复杂的检查逻辑，确保本地和云端数据始终一致
            const needsScoreSync = true;
            const currentTopScores = GameData.getTopScores(gameMode);

            console.log(`GameDataManager: 本局分数:${finalScore}, 当前记录:[${currentTopScores.join(', ')}], 总是同步游戏记录`);

            // 🎯 步骤2：一次性同步金币和所有游戏记录（最优化方案）
            const totalCoins = GameData.getCoin();

            // 收集所有本地游戏记录
            const allTopScores = {
                [GameMode.NORMAL_EASY]: GameData.getTopScores(GameMode.NORMAL_EASY),
                [GameMode.NORMAL_STANDARD]: GameData.getTopScores(GameMode.NORMAL_STANDARD),
                [GameMode.NORMAL_HARD]: GameData.getTopScores(GameMode.NORMAL_HARD),
                [GameMode.CHALLENGE_WIND]: GameData.getTopScores(GameMode.CHALLENGE_WIND),
                [GameMode.CHALLENGE_FOG]: GameData.getTopScores(GameMode.CHALLENGE_FOG),
                [GameMode.CHALLENGE_SNOW]: GameData.getTopScores(GameMode.CHALLENGE_SNOW)
            };

            console.log(`GameDataManager: 一次性同步 - 金币:${totalCoins}, 所有关卡分数:`, allTopScores);

            // 🔧 添加重试机制
            let syncSuccess = false;
            let retryCount = 0;
            const maxRetries = 2;

            while (!syncSuccess && retryCount < maxRetries) {
                if (retryCount > 0) {
                    console.log(`GameDataManager: 数据同步重试 ${retryCount}/${maxRetries}`);
                    // 等待1秒后重试
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // 🎯 关键优化：一次调用同时更新金币和所有游戏记录
                syncSuccess = await this.cloudDB.updatePlayerCoinsAndAllScores(totalCoins, allTopScores);
                retryCount++;
            }

            if (syncSuccess) {
                console.log('GameDataManager: 金币和游戏记录一次性同步完成（1次调用）');
                console.log('GameDataManager: 🎯 优化成功：每局游戏只消耗1次云调用');
            } else {
                console.error('GameDataManager: 数据同步失败，已重试' + maxRetries + '次');
                // 恢复自动同步设置
                this.setAutoSyncEnabled(originalAutoSync);
                return false;
            }

            // 🔧 重要：恢复自动同步设置，但延迟5秒，给云端更新一些时间
            setTimeout(() => {
                this.setAutoSyncEnabled(originalAutoSync);
                console.log('GameDataManager: 已恢复自动同步设置');
            }, 5000);

            return true;
        } catch (error) {
            console.error('GameDataManager: 同步游戏结束数据失败', error);
            // 确保在异常情况下也恢复自动同步设置
            this.setAutoSyncEnabled(originalAutoSync);
            return false;
        }
    }

    /**
     * 🔧 高效方法：模拟保存前的topScores状态
     * 通过当前topScores反推保存前的状态，避免额外的云调用
     */
    private getTopScoresBeforeSave(gameMode: GameMode, finalScore: number): number[] {
        const currentTopScores = GameData.getTopScores(gameMode);

        // 如果当前topScores中包含finalScore，说明这个分数是刚刚添加的
        // 我们需要移除这个分数来还原保存前的状态
        const beforeScores = [...currentTopScores];

        // 找到并移除最后添加的finalScore（如果存在）
        const scoreIndex = beforeScores.indexOf(finalScore);
        if (scoreIndex !== -1) {
            beforeScores.splice(scoreIndex, 1);
            // 补齐到3个元素
            while (beforeScores.length < 3) {
                beforeScores.push(0);
            }
        }

        return beforeScores;
    }

    /**
     * 检查本局游戏是否产生新记录
     */
    private checkIfNewRecord(gameMode: GameMode, finalScore: number, currentTopScores: number[]): boolean {
        // 如果当前记录不足3个，或者本局分数超过最低记录，就是新记录
        if (currentTopScores.length < 3) {
            return true;
        }

        // 检查是否超过当前最低记录（第3名）
        const lowestRecord = currentTopScores[2] || 0;
        return finalScore > lowestRecord;
    }

    /**
     * 获取云端玩家数据
     */
    public async getCloudPlayerData(): Promise<any> {
        if (!this.isInitialized) {
            return null;
        }

        return await this.cloudDB.getMyPlayerData();
    }

    /**
     * 获取玩家排名
     */
    public async getPlayerRank(gameMode: GameMode): Promise<{ rank: number; score: number } | null> {
        if (!this.isInitialized) {
            return null;
        }

        return await this.cloudDB.getPlayerRank(gameMode);
    }

    /**
     * 获取排行榜数据
     */
    public async getLeaderboard(gameMode: GameMode, limit: number = 50): Promise<any[]> {
        if (!this.isInitialized) {
            return [];
        }

        return await this.cloudDB.getLeaderboard(gameMode, limit);
    }

    /**
     * 更新玩家资料
     */
    public async updateProfile(nickname?: string, avatarUrl?: string): Promise<boolean> {
        if (!this.isInitialized) {
            return false;
        }

        return await this.cloudDB.updatePlayerProfile(nickname, avatarUrl);
    }

    /**
     * 同步本地数据到云端
     */
    public async syncLocalDataToCloud(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log('GameDataManager: 开始同步本地数据到云端');

            // 同步所有关卡的最高分
            const gameModes = [
                GameMode.NORMAL_EASY,
                GameMode.NORMAL_STANDARD,
                GameMode.NORMAL_HARD,
                GameMode.CHALLENGE_WIND,
                GameMode.CHALLENGE_FOG,
                GameMode.CHALLENGE_SNOW
            ];

            for (const mode of gameModes) {
                const localScore = GameData.getBestScore(mode);
                if (localScore > 0) {
                    await this.cloudDB.updatePlayerScore(mode, localScore);
                }
            }

            // 同步金币
            const localCoins = GameData.getCoin();
            if (localCoins > 0) {
                await this.cloudDB.updatePlayerCoins(localCoins, 'set');
            }

            console.log('GameDataManager: 本地数据同步完成');
        } catch (error) {
            console.error('GameDataManager: 同步本地数据失败', error);
        }
    }

    /**
     * 从云端加载数据到本地
     */
    public async loadCloudDataToLocal(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log('GameDataManager: 开始从云端加载数据到本地');

            // 临时禁用自动同步，避免循环调用
            const originalAutoSync = this.autoSyncEnabled;
            this.setAutoSyncEnabled(false);

            const cloudData = await this.cloudDB.getMyPlayerData();
            if (!cloudData) {
                console.warn('GameDataManager: 云端没有用户数据');
                // 恢复自动同步设置
                this.setAutoSyncEnabled(originalAutoSync);
                return;
            }

            // 加载金币
            if (cloudData.coins > GameData.getCoin()) {
                GameData.setCoin(cloudData.coins);
                console.log(`GameDataManager: 更新本地金币为 ${cloudData.coins}`);
            }

            // 🔧 修复：谨慎加载各关卡分数数据，避免覆盖本地正确记录
            for (const [gameMode, scores] of Object.entries(cloudData.topScores)) {
                if (scores && Array.isArray(scores) && scores.length > 0) {
                    const cloudBest = Math.max(...scores);
                    const localBest = GameData.getBestScore(gameMode as GameMode);
                    const localTopScores = GameData.getTopScores(gameMode as GameMode);
                    const cloudTopScores = scores.slice(0, 3); // 确保只取前3个

                    console.log(`GameDataManager: 检查${gameMode}数据 - 云端最高分:${cloudBest}, 本地最高分:${localBest}`);
                    console.log(`  云端前三分: [${cloudTopScores.join(', ')}]`);
                    console.log(`  本地前三分: [${localTopScores.join(', ')}]`);

                    // 🔧 只有在云端数据明显更好时才更新本地数据
                    // 避免云端的错误数据覆盖本地正确数据
                    if (cloudBest > localBest && cloudBest > 0) {
                        GameData.setBestScore(gameMode as GameMode, cloudBest);
                        console.log(`GameDataManager: 更新本地${gameMode}最高分为 ${cloudBest}`);
                    } else {
                        console.log(`GameDataManager: 保持本地${gameMode}最高分 ${localBest}`);
                    }

                    // 🔧 重要修复：更加谨慎的合并策略
                    // 只有当云端有明显更好的数据时才进行合并
                    const localHasValidScores = localTopScores.some(score => score > 0);
                    const cloudHasValidScores = cloudTopScores.some(score => score > 0);

                    if (cloudHasValidScores && (!localHasValidScores || cloudBest > localBest)) {
                        // 只有在云端有有效分数且本地没有，或者云端最高分更好时才合并
                        const mergedScores = this.mergeTopScores(localTopScores, cloudTopScores);
                        if (JSON.stringify(mergedScores) !== JSON.stringify(localTopScores)) {
                            this.updateLocalTopScores(gameMode as GameMode, mergedScores);
                            console.log(`GameDataManager: 合并更新本地${gameMode}的topScores为 [${mergedScores.join(', ')}]`);
                        }
                    } else {
                        console.log(`GameDataManager: 保持本地${gameMode}的topScores [${localTopScores.join(', ')}]`);
                    }
                }
            }

            // 恢复自动同步设置
            this.setAutoSyncEnabled(originalAutoSync);

            console.log('GameDataManager: 云端数据加载完成');
        } catch (error) {
            console.error('GameDataManager: 加载云端数据失败', error);
            // 确保在异常情况下也恢复自动同步设置
            this.setAutoSyncEnabled(true);
        }
    }

    /**
     * 强制同步所有待处理的数据
     */
    public async forceSync(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        await this.syncManager.forceSync();
    }

    /**
     * 手动检查云端数据变化（仅在需要时调用，节省调用次数）
     * 建议在以下时机调用：
     * 1. 游戏从后台切换到前台时
     * 2. 用户主动刷新时
     * 3. 进入排行榜界面时
     */
    public async manualSyncCloudChanges(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        try {
            console.log('GameDataManager: 手动检查云端数据变化');
            await this.checkAndSyncCloudChanges();
        } catch (error) {
            console.error('GameDataManager: 手动云端同步失败', error);
        }
    }

    /**
     * 🔧 简化：移除游戏生命周期监听，只在游戏启动时同步一次
     * 按照用户建议，不再在切换前台时自动同步
     */
    public startGameLifecycleSync(): void {
        console.log('GameDataManager: 已简化同步策略，只在游戏启动时同步一次');
        // 不再监听wx.onShow事件
    }

    /**
     * 检查并同步云端数据变化
     */
    private async checkAndSyncCloudChanges(): Promise<void> {
        try {
            const cloudData = await this.cloudDB.getMyPlayerData();
            if (!cloudData) {
                return;
            }

            // 检查金币变化
            const localCoins = GameData.getCoin();
            if (cloudData.coins !== localCoins) {
                console.log(`GameDataManager: 检测到云端金币变化 ${localCoins} → ${cloudData.coins}`);

                // 临时禁用自动同步，避免循环调用
                const originalAutoSync = this.autoSyncEnabled;
                this.setAutoSyncEnabled(false);

                GameData.setCoin(cloudData.coins);

                // 恢复自动同步
                this.setAutoSyncEnabled(originalAutoSync);

                console.log(`GameDataManager: 已同步云端金币到本地: ${cloudData.coins}`);
            }

            // 🔧 修复：谨慎检查分数变化，避免错误的云端数据覆盖本地正确数据
            for (const [gameMode, scores] of Object.entries(cloudData.topScores)) {
                if (scores && Array.isArray(scores) && scores.length > 0) {
                    const cloudBest = Math.max(...scores);
                    const localBest = GameData.getBestScore(gameMode as GameMode);
                    const localTopScores = GameData.getTopScores(gameMode as GameMode);

                    // 比较云端和本地的topScores数组
                    const cloudTopScores = scores.slice(0, 3); // 确保只取前3个

                    console.log(`GameDataManager: 检查云端${gameMode}数据变化`);
                    console.log(`  本地: 最高分=${localBest}, 前三分=[${localTopScores.join(', ')}]`);
                    console.log(`  云端: 最高分=${cloudBest}, 前三分=[${cloudTopScores.join(', ')}]`);

                    // 🔧 重要修复：更加谨慎的同步策略
                    let needsUpdate = false;
                    const localHasValidScores = localTopScores.some(score => score > 0);
                    const cloudHasValidScores = cloudTopScores.some(score => score > 0);

                    // 只有在云端明显更好时才同步
                    if (cloudBest > localBest && cloudBest > 0) {
                        needsUpdate = true;
                        console.log(`GameDataManager: 云端${gameMode}最高分更好，需要同步`);
                    } else if (cloudHasValidScores && !localHasValidScores) {
                        // 只有当云端有有效分数而本地没有时才同步
                        needsUpdate = true;
                        console.log(`GameDataManager: 本地${gameMode}无有效分数，从云端同步`);
                    } else {
                        console.log(`GameDataManager: ${gameMode}数据无需同步，保持本地数据`);
                    }

                    if (needsUpdate) {
                        // 临时禁用自动同步
                        const originalAutoSync = this.autoSyncEnabled;
                        this.setAutoSyncEnabled(false);

                        // 更新最高分（只有在云端更好时）
                        if (cloudBest > localBest && cloudBest > 0) {
                            GameData.setBestScore(gameMode as GameMode, cloudBest);
                        }

                        // 合并并更新topScores数组
                        const mergedScores = this.mergeTopScores(localTopScores, cloudTopScores);
                        this.updateLocalTopScores(gameMode as GameMode, mergedScores);

                        // 恢复自动同步
                        this.setAutoSyncEnabled(originalAutoSync);

                        console.log(`GameDataManager: 已同步云端${gameMode}数据到本地`);
                    } else {
                        console.log(`GameDataManager: ${gameMode}数据无需同步`);
                    }
                }
            }
        } catch (error) {
            console.error('GameDataManager: 检查云端数据变化失败', error);
        }
    }

    /**
     * 合并本地和云端的topScores数组
     */
    private mergeTopScores(localScores: number[], cloudScores: number[]): number[] {
        // 🔧 重要修复：如果云端数据全是0，直接返回本地数据，避免错误合并
        const cloudHasValidScores = cloudScores.some(score => score > 0);
        if (!cloudHasValidScores) {
            // console.log(`🔍 [mergeTopScores] 云端无有效分数，保持本地数据: [${localScores.join(', ')}]`);
            return [...localScores]; // 返回本地数据的副本
        }

        // 合并所有分数
        const allScores = [...localScores, ...cloudScores];

        // 去重并排序（降序）
        const uniqueScores = Array.from(new Set(allScores))
            .filter(score => score >= 0) // 保留0分
            .sort((a, b) => b - a);

        // 只保留前3个，并确保数组长度为3
        const topThree = uniqueScores.slice(0, 3);

        // 补齐到3个元素
        while (topThree.length < 3) {
            topThree.push(0);
        }

        return topThree;
    }

    /**
     * 更新本地topScores数组
     */
    private updateLocalTopScores(gameMode: GameMode, newTopScores: number[]): void {
        try {
            const topScoresKey = `TopScores_Mode_${gameMode}`;
            localStorage.setItem(topScoresKey, JSON.stringify(newTopScores));
            console.log(`GameDataManager: 已更新本地${gameMode}的topScores: [${newTopScores.join(', ')}]`);
        } catch (error) {
            console.error('GameDataManager: 更新本地topScores失败', error);
        }
    }

    /**
     * 获取同步状态
     */
    public getSyncStatus(): any {
        if (!this.syncManager) {
            return { pendingScores: 0, pendingCoins: false, retryCount: 0 };
        }

        return this.syncManager.getSyncStatus();
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        if (this.syncManager) {
            this.syncManager.cleanup();
        }
        this.isInitialized = false;
    }
}
